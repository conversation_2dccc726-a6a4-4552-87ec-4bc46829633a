<?php

namespace Tests\Feature;


use App\Models\O2oErrandOrder;
use App\Models\Merchant;
use App\Models\User;
use App\Models\Rider;
use App\Services\HaiboService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class HaiboStatusCallbackTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $merchant;
    protected $order;
    protected $rider;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->user = User::factory()->create();

        // 创建海博商户
        $this->merchant = Merchant::factory()->create([
            'user_id' => $this->user->id,
            'merchant_type' => 'haibo',
        ]);

        // 创建骑手
        $this->rider = Rider::factory()->create();

        // 创建海博订单
        $this->order = O2oErrandOrder::factory()->create([
            'user_id' => $this->user->id,
            'app_key' => O2oErrandOrder::APP_KEY_HB,
            'out_order_no' => 'HB_TEST_' . time(),
            'rider_id' => $this->rider->id,
            'order_status' => O2oErrandOrder::STATUS_PICKUP,
        ]);
    }

    /** @test */
    public function it_can_trigger_status_callback_via_api()
    {
        // Mock HTTP 请求
        Http::fake([
            '*' => Http::response(['code' => 0, 'message' => 'success'], 200)
        ]);

        $response = $this->postJson('/api/delivery/statusCallback', [
            'orderId' => $this->order->out_order_no
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 0,
                    'message' => '回调成功'
                ]);
    }

    /** @test */
    public function it_returns_error_for_non_existent_order()
    {
        $response = $this->postJson('/api/delivery/statusCallback', [
            'orderId' => 'NON_EXISTENT_ORDER'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => HaiboService::RESULT_ORDER_NOT_EXIST,
                    'message' => '订单不存在'
                ]);
    }

    /** @test */
    public function it_validates_required_parameters()
    {
        $response = $this->postJson('/api/delivery/statusCallback', []);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => HaiboService::RESULT_PARAM_ERROR
                ]);
    }

    /** @test */
    public function it_can_build_callback_data_correctly()
    {
        $haiboService = new HaiboService();

        // 使用反射访问私有方法
        $reflection = new \ReflectionClass($haiboService);
        $method = $reflection->getMethod('buildCallbackData');
        $method->setAccessible(true);

        $otherData = [
            'rider_name' => $this->rider->name,
            'rider_phone' => $this->rider->phone,
            'longitude' => 120.123456,
            'latitude' => 30.123456,
        ];

        $callbackData = $method->invoke($haiboService, $this->order, HaiboService::HB_STATUS_ACCEPTED, $otherData);

        // 验证必填字段
        $this->assertArrayHasKey('orderId', $callbackData);
        $this->assertArrayHasKey('carrierDeliveryId', $callbackData);
        $this->assertArrayHasKey('status', $callbackData);
        $this->assertArrayHasKey('operateTime', $callbackData);
        $this->assertArrayHasKey('actualFee', $callbackData);

        // 验证骑手信息（已接单状态必传）
        $this->assertArrayHasKey('riderName', $callbackData);
        $this->assertArrayHasKey('riderPhone', $callbackData);
        $this->assertArrayHasKey('riderPhoneType', $callbackData);
        $this->assertArrayHasKey('riderLng', $callbackData);
        $this->assertArrayHasKey('riderLat', $callbackData);

        // 验证费用字段
        $this->assertArrayHasKey('tollFee', $callbackData);
        $this->assertArrayHasKey('handlingFee', $callbackData);
        $this->assertArrayHasKey('waitingFee', $callbackData);
        $this->assertArrayHasKey('parkingFee', $callbackData);

        // 验证字段值
        $this->assertEquals($this->order->out_order_no, $callbackData['orderId']);
        $this->assertEquals($this->order->order_no, $callbackData['carrierDeliveryId']);
        $this->assertEquals(HaiboService::HB_STATUS_ACCEPTED, $callbackData['status']);
        $this->assertEquals($this->rider->name, $callbackData['riderName']);
        $this->assertEquals($this->rider->phone, $callbackData['riderPhone']);
        $this->assertEquals(0, $callbackData['riderPhoneType']); // 真实号

        // 验证坐标格式（应该是整数，坐标*10^6）
        $this->assertIsInt($callbackData['riderLng']);
        $this->assertIsInt($callbackData['riderLat']);
        $this->assertEquals(120123456, $callbackData['riderLng']);
        $this->assertEquals(30123456, $callbackData['riderLat']);
    }

    /** @test */
    public function it_can_get_correct_haibo_status()
    {
        $haiboService = new HaiboService();

        // 测试不同订单状态的映射
        $testCases = [
            O2oErrandOrder::STATUS_PAID => HaiboService::HB_STATUS_CREATED,
            O2oErrandOrder::STATUS_PICKUP => HaiboService::HB_STATUS_ACCEPTED,
            O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT => HaiboService::HB_STATUS_ARRIVED_STORE,
            O2oErrandOrder::STATUS_DELIVERY => HaiboService::HB_STATUS_PICKED_UP,
            O2oErrandOrder::STATUS_FINISH => HaiboService::HB_STATUS_DELIVERED,
        ];

        foreach ($testCases as $systemStatus => $expectedHaiboStatus) {
            $this->order->order_status = $systemStatus;
            $this->order->refund_status = O2oErrandOrder::REFUND_STATUS_INIT;

            $actualStatus = $haiboService->getHaiboStatus($this->order);
            $this->assertEquals($expectedHaiboStatus, $actualStatus);
        }

        // 测试取消状态
        $this->order->refund_status = O2oErrandOrder::REFUND_STATUS_SUCCESS;
        $cancelStatus = $haiboService->getHaiboStatus($this->order);
        $this->assertEquals(HaiboService::HB_STATUS_CANCELLED, $cancelStatus);
    }

    /** @test */
    public function it_calls_haibo_callback_when_order_status_changes()
    {
        // Mock HTTP 请求
        Http::fake([
            '*' => Http::response(['code' => 0, 'message' => 'success'], 200)
        ]);

        // 模拟订单状态变更
        dispatch(new \App\Jobs\DispatchOpenCallback($this->order));

        // 验证HTTP请求被发送
        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'statusCallback');
        });
    }

    /** @test */
    public function it_handles_callback_failure_gracefully()
    {
        // Mock HTTP 请求失败
        Http::fake([
            '*' => Http::response(['code' => 1, 'message' => 'error'], 500)
        ]);

        $haiboService = new HaiboService();
        $success = $haiboService->deliveryStatusCallback(
            $this->order->user_id,
            $this->order->order_no,
            $this->order->out_order_no,
            HaiboService::HB_STATUS_ACCEPTED,
            []
        );

        $this->assertFalse($success);
    }

    /** @test */
    public function it_includes_cancel_information_for_cancelled_orders()
    {
        // 设置订单为已取消状态
        $this->order->refund_status = O2oErrandOrder::REFUND_STATUS_SUCCESS;
        $this->order->close_reason = '商户取消';
        $this->order->save();

        $haiboService = new HaiboService();

        // 使用反射访问私有方法
        $reflection = new \ReflectionClass($haiboService);
        $method = $reflection->getMethod('buildCallbackData');
        $method->setAccessible(true);

        $callbackData = $method->invoke($haiboService, $this->order, HaiboService::HB_STATUS_CANCELLED, []);

        $this->assertArrayHasKey('cancelReasonCode', $callbackData);
        $this->assertArrayHasKey('cancelReasonDesc', $callbackData);
        $this->assertArrayHasKey('cancelFee', $callbackData);
        $this->assertEquals('商户取消', $callbackData['cancelReasonDesc']);

        // 验证取消状态下不包含骑手信息
        $this->assertArrayNotHasKey('riderName', $callbackData);
        $this->assertArrayNotHasKey('riderPhone', $callbackData);
    }

    /** @test */
    public function it_includes_required_rider_info_for_active_statuses()
    {
        // 测试需要骑手信息的状态
        $activeStatuses = [
            HaiboService::HB_STATUS_ACCEPTED,      // 已接单
            HaiboService::HB_STATUS_ARRIVED_STORE, // 已到店
            HaiboService::HB_STATUS_PICKED_UP,     // 已取货
            HaiboService::HB_STATUS_DELIVERED,     // 已送达
        ];

        $haiboService = new HaiboService();
        $reflection = new \ReflectionClass($haiboService);
        $method = $reflection->getMethod('buildCallbackData');
        $method->setAccessible(true);

        foreach ($activeStatuses as $status) {
            $callbackData = $method->invoke($haiboService, $this->order, $status, [
                'longitude' => 120.123456,
                'latitude' => 30.123456,
            ]);

            // 验证骑手信息必传字段
            $this->assertArrayHasKey('riderName', $callbackData, "Status {$status} should include riderName");
            $this->assertArrayHasKey('riderPhone', $callbackData, "Status {$status} should include riderPhone");
            $this->assertArrayHasKey('riderPhoneType', $callbackData, "Status {$status} should include riderPhoneType");
            $this->assertArrayHasKey('riderLng', $callbackData, "Status {$status} should include riderLng");
            $this->assertArrayHasKey('riderLat', $callbackData, "Status {$status} should include riderLat");
        }
    }

    /** @test */
    public function it_excludes_rider_info_for_inactive_statuses()
    {
        // 测试不需要骑手信息的状态
        $inactiveStatuses = [
            HaiboService::HB_STATUS_CREATED,   // 已创建
            HaiboService::HB_STATUS_CANCELLED, // 已取消
        ];

        $haiboService = new HaiboService();
        $reflection = new \ReflectionClass($haiboService);
        $method = $reflection->getMethod('buildCallbackData');
        $method->setAccessible(true);

        foreach ($inactiveStatuses as $status) {
            // 对于取消状态，需要设置退款状态
            if ($status == HaiboService::HB_STATUS_CANCELLED) {
                $this->order->refund_status = O2oErrandOrder::REFUND_STATUS_SUCCESS;
                $this->order->close_reason = '测试取消';
            }

            $callbackData = $method->invoke($haiboService, $this->order, $status, []);

            // 验证不包含骑手信息
            $this->assertArrayNotHasKey('riderName', $callbackData, "Status {$status} should not include riderName");
            $this->assertArrayNotHasKey('riderPhone', $callbackData, "Status {$status} should not include riderPhone");
            $this->assertArrayNotHasKey('riderPhoneType', $callbackData, "Status {$status} should not include riderPhoneType");
            $this->assertArrayNotHasKey('riderLng', $callbackData, "Status {$status} should not include riderLng");
            $this->assertArrayNotHasKey('riderLat', $callbackData, "Status {$status} should not include riderLat");
        }
    }

    /** @test */
    public function it_generates_correct_haibo_signature()
    {
        $haiboService = new HaiboService();

        // 测试海博文档中的签名示例
        $testResult = $haiboService->testSignature();

        $this->assertTrue($testResult['is_correct'],
            "签名生成不正确。期望: {$testResult['expected_sign']}, 实际: {$testResult['generated_sign']}"
        );
    }

    /** @test */
    public function it_sends_callback_with_correct_protocol()
    {
        // Mock HTTP 请求
        Http::fake([
            '*' => Http::response(['code' => 0, 'message' => '成功'], 200)
        ]);

        $haiboService = new HaiboService();
        $success = $haiboService->deliveryStatusCallback(
            $this->order->user_id,
            $this->order->order_no,
            $this->order->out_order_no,
            HaiboService::HB_STATUS_ACCEPTED,
            [
                'rider_name' => $this->rider->name,
                'rider_phone' => $this->rider->phone,
                'longitude' => 120.123456,
                'latitude' => 30.123456,
            ]
        );

        $this->assertTrue($success);

        // 验证请求格式
        Http::assertSent(function ($request) {
            // 验证请求头
            $this->assertEquals('application/x-www-form-urlencoded', $request->header('Content-Type')[0]);

            // 验证包含海博协议公共参数
            $data = $request->data();
            $this->assertArrayHasKey('developerId', $data);
            $this->assertArrayHasKey('timestamp', $data);
            $this->assertArrayHasKey('version', $data);
            $this->assertArrayHasKey('sign', $data);
            $this->assertEquals('1.0', $data['version']);

            return true;
        });
    }
}
